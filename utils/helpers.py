import time
from typing import Optional

from playwright.sync_api import Page

from pages.base_page import BasePage
from utils.config import BASE_URL, ENVIRONMENT, USER_PHONE_NUMBER, USER_TYPE, USER_ID
from utils.logger import logger

class LoginHelper(BasePage):
    def __init__(self, page: Page):
        super().__init__(page)

    def user_login(self, usertype: str) -> None:
        """
        :param usertype: A string representing the type of user (basic or pro)
        :raises TypeError: If the input is not a string
        """
        if not isinstance(usertype, str):
            raise TypeError("Input must be a string")

        if usertype.lower() == 'basic':
            self.phone_login('basic')
        elif usertype.lower() == 'pro':
            self.phone_login('pro')
        else:
            raise ValueError("Invalid user type. Must be 'basic' or 'pro'")

    def phone_login(self, user_type: str) -> None:
        """
        :param user_type: A string representing the type of user (basic or pro)
        """
        logger.info(f"Logging in as {user_type} user...")
        self.page.goto(BASE_URL[ENVIRONMENT])
        self.page.get_by_role("button", name="Login").click()
        self.page.get_by_placeholder("Phone Number").click()
        self.page.get_by_placeholder("Phone Number").fill(USER_PHONE_NUMBER[ENVIRONMENT][user_type])
        self.page.get_by_role("button", name="Get OTP").click()
        self.page.locator("//input[contains(@aria-label,'Digit 1')]").click()
        for i in range(1, 5):
            self.page.locator(f"//input[contains(@aria-label,'Digit {i}')]").fill("9")
        time.sleep(3)  # waiting for modal to disappear
        assert self.page.get_by_text("Logged in successfully").is_visible(), \
            "Logged in successfully text is not visible!"
        logger.info("Logged in successfully!")

    def logout(self) -> None:
        """
        Verifying if the user is logged in and then logging the user out
        """
        logger.info("Checking if the user is logged in and logging out...")
        assert self.page.locator("section.name-holder").text_content() == "Account", \
            "Account text does not match!"
        self.page.get_by_text("Account", exact=True).click()
        self.page.locator("a").filter(has_text="Logout").click()
        assert self.page.locator('span:has-text("Login")').text_content() == "Login", \
            "Login text does not match!"
        logger.info("Logged out successfully!")

class CommonHelper(BasePage):
    def __init__(self, page: Page):
        super().__init__(page)

    def close_web_ad_unit(self, user_type: str = None)-> None:
        logger.info("Closing web ad unit...")
        cookie_name: str = "web-ad-unit"
        if user_type is None:
            user_type = USER_TYPE.LOGGED_OUT
        if user_type != USER_TYPE.LOGGED_OUT:
            cookie_name = "web-ad-"+USER_ID[ENVIRONMENT][user_type]

        self.page.context.add_cookies([{
                "name": cookie_name,
                "value": "true",
                "domain": "tickertape.in",
                "path": "/",
                "expire": -1
            }])
        self.page.reload()
        if self.page.locator("i.icon-Close").count() != 0:
            self.page.locator("i.icon-Close").click()
