
from playwright.sync_api import Page

from pages.base_page import BasePage
from utils.logger import logger


class StockPage(BasePage):
    def __init__(self, page: Page):
        super().__init__(page)

    METRICS_SECTION = "//div[@data-section-tag='key-metrics']"
    KEY_METRICS_LOCK_ICON = "//h2[contains(text(),'Key Metrics')]/../button/i"
    KEY_METRICS_EDIT_ICON = "//i[contains (@class, 'icon-edit')]"
    PRO_PLANS_POPUP = "//button[contains(text(),'See Tickertape Pro Plans')]"
    LOGIN_MODAL_CLOSE_ICON = "//i[contains(@class,'icon-Close')]"
    FORECAST_AND_RATINGS_SECTION = "//div[@data-section-tag='forecastRatings']"
    ANALYST_PERCENTAGE = "span.percBuyReco-value"
    OVERVIEW_SECTION = "(//div[contains(@class,'chart-scope-picker')])[1]"
    SECTOR_NAME_BUTTON = "(//*[contains(@class, 'stock-label-title ')])[4]"
    SUBSECTOR_TAG_BUTTON = "(//*[contains(@class, 'internal-tags')])[2]"
    COLLECTION_PAGE_SECTOR_EXPAND = "//div[contains(@class,'sector-display')]"
    COLLECTION_PAGE_SELECTED_SUBSECTOR_EXPAND =("//i[contains(@class,"
                                        "'icon-checkbox-intermediate')]/../../../../following-sibling::div/i")


    def move_to_stock_page_section(self, section_name: str) -> None:
        """
        :param section_name: Name of the section to move to ('key metrics', 'forecast and ratings', 'overview')
        :return: None
        """
        logger.info(f"Moving to {section_name} section")
        section_map = {
            "overview" : self.OVERVIEW_SECTION,
            "key metrics": self.METRICS_SECTION,
            "forecast and ratings": self.FORECAST_AND_RATINGS_SECTION
        }
        section_locator = section_map.get(section_name.lower())
        if section_locator:
            self.page.locator(section_locator).scroll_into_view_if_needed()
        else:
            logger.error(f"Section '{section_name}' not found.")

    def check_key_metrics(self, user_type: str) -> None:
        """
        :param user_type: Type of user ('pro' or 'basic' or 'logged_out')
        :return: None
        """
        logger.info(f"Checking key metrics for {user_type} user")
        match user_type.lower():
            case "pro":
                assert self.page.locator(self.KEY_METRICS_EDIT_ICON).is_visible(), \
                    "Edit icon is not visible for pro user"
            case "basic":
                self.page.locator(self.KEY_METRICS_LOCK_ICON).click(force=True), \
                    "Key metrics lock icon is not visible"
                assert self.page.locator(self.PRO_PLANS_POPUP).count() == 1, \
                    "Pro plans popup is not visible for basic user"
                self.page.mouse.click(0,0)
            case "logged_out":
                self.page.locator(self.KEY_METRICS_LOCK_ICON).click(force=True)
                assert self.page.locator(self.PRO_PLANS_POPUP).count() == 0, \
                    "Pro plans popup is not visible for logged out user"
                self.page.locator(self.LOGIN_MODAL_CLOSE_ICON).nth(0).click()
            case _:
                logger.error(f"Unexpected user type: {user_type}")

    def verify_key_metrics_section(self, user_type: str) -> None:
        logger.info(f"Checking key metricese for {user_type} user")
        self.move_to_stock_page_section("key metrics")
        self.page.wait_for_timeout(1000)
        self.check_key_metrics(user_type)
        logger.info(f"Key metrics verified for {user_type} user")

    def verify_forecast_and_ratings_section(self, user_type) -> None:
        logger.info(f"Checking forecast and ratings for {user_type} user")
        self.move_to_stock_page_section("forecast and ratings")
        self.page.wait_for_timeout(1000)
        self.check_analyst_suggestion()
        self.check_upside_and_growth(user_type)
        logger.info(f"Forecast and ratings verified for {user_type} user")

    def check_analyst_suggestion(self):
        logger.info("Checking analyst suggestion")
        percent_value = self.page.locator(self.ANALYST_PERCENTAGE).text_content()
        assert percent_value != "", "Analyst suggestion is not visible"
        assert "%" in percent_value, "Analyst suggestion does not contain % symbol"
        assert self.page.get_by_text("Analysts have suggested that").text_content().strip() == ("Analysts have suggested "
                                                                                          "that investors can buy "
                                                                                                "this stock"), \
            "Analyst heading text not matching"
        assert "analysts" in self.page.locator(".forecast-radial > div > p").text_content()
        logger.info("Analyst suggestion verified")

    def check_upside_and_growth(self, user_type):
        pass

    def get_sector_name(self) -> str :
        return self.page.locator(self.SECTOR_NAME_BUTTON).text_content()

    def get_subsector_name(self) -> str :
        return self.page.locator(self.SUBSECTOR_TAG_BUTTON).text_content()

    def check_sector_collection_page_redirection(self):
        logger.info("Checking sector collection page redirection")
        self.move_to_stock_page_section("overview")
        sector_locator = self.page.locator(self.SECTOR_NAME_BUTTON)
        sector_name = sector_locator.text_content()
        sector_locator.click()
        self.page.locator(self.COLLECTION_PAGE_SECTOR_EXPAND).click()
        COLLECTION_PAGE_SECTOR_CHECKBOX = f"//span[text()='{sector_name}']/preceding-sibling::span/i[contains(@class,'icon-checkbox-done')]"
        assert self.page.locator(COLLECTION_PAGE_SECTOR_CHECKBOX).is_visible(),f" {sector_name} Sector is not selected on collection page"
        self.page.go_back()

    def check_subsector_collection_page_redirection(self):
        logger.info("Checking sub sector collection page redirection")
        self.move_to_stock_page_section("overview")
        subsector_locator = self.page.locator(self.SUBSECTOR_TAG_BUTTON)
        subsector_name = subsector_locator.text_content()
        # check if the subsector_locator is clickable
        if subsector_locator.is_enabled():
            logger.info(f"subsector({subsector_name}) is clickable")
            subsector_locator.click()
            self.page.locator(self.COLLECTION_PAGE_SECTOR_EXPAND).click()
            self.page.locator(self.COLLECTION_PAGE_SELECTED_SUBSECTOR_EXPAND).click()
            COLLECTION_PAGE_SUBSECTOR_CHECKBOX = f"//span[text()='{subsector_name}']/preceding-sibling::span/i[contains(@class,'icon-checkbox-done')]"
            try:
                assert self.page.locator(COLLECTION_PAGE_SUBSECTOR_CHECKBOX).is_visible(),f" {subsector_name} Sub-sector is not selected on collection page"
            except AssertionError:
                logger.error(f" {subsector_name} Sub-sector is not selected on collection page")
            self.page.go_back()
        else:
            logger.warning(f"Subsector ({subsector_name}) is not clickable for {subsector_name}")
