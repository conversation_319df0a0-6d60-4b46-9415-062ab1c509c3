
from playwright.sync_api import Page, Locator

from pages.base_page import BasePage
from utils.config import ENVIRONMENT, BASE_URL, ASSET_TYPE
from utils.logger import logger


class AssetPage(BasePage):
    def __init__(self, page: Page):
        super().__init__(page)

    CHART_HIGH_PRICE_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div[contains(text(),'High')]"
    CHART_HIGH_PRICE_VALUE = "//div[contains(text(),'High')]/../div[2]"
    CHART_LOW_PRICE_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div[contains(text(),'Low')]"
    CHART_LOW_PRICE_VALUE = "//div[contains(text(),'Low')]/../div[2]"
    CHARTS_RETURNS_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div[contains(text(),'Return')]"
    CHARTS_RETURNS_VALUE = "//div[contains(text(),'Returns')]/../div[2]"
    CAGR_LABEL = "//h2[contains(text(),'Price Chart')]/parent::div/descendant::div[contains(text(),'CAGR')]"
    CAGR_VALUE = "//div[contains(text(),'CAGR')]/../div[2]"
    CHART_LINE = "//*[@class='rv-xy-plot__inner']/*[name()='path']"
    INVESTMENT_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div[contains(text(),'Investment')]"
    INVESTMENT_VALUE = "//div[contains(text(), 'Investment')]/../div[2]"
    VALUE_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div[contains(text(),'Value')]"
    VALUE_DATA = "//div[contains(text(), 'Value')]/../div[2]"
    MAX_CAGR_LABEL = "//h2[contains(text(),'Price Chart')]/../..//div/span[contains(text(),'CAGR')]"
    MAX_CAGR_VALUE = "//h2[contains(text(),'Price Chart')]/../descendant::span[1]/../following-sibling::div"



    def navigate_to_asset_page(self,asset_type:str ,asset_id: str) -> None:
        """
        :param asset_type: asset page to navigate to  (stocks, etfs, indices, mutualfunds)
        :param asset_id: id of the asset to navigate to
        :return: None
        """
        logger.info(f"Navigating to {asset_type} page with sid: {asset_id}")
        asset_type = asset_type.lower()
        if asset_type not in ["stocks", "etfs", "indices", "mutualfunds"]:
            raise ValueError(f"Invalid asset type: {asset_type}, accepted values: stocks, mutualfunds, etfs, indices")

        self.page.goto(f"{BASE_URL[ENVIRONMENT]}/{asset_type}/{asset_id}")
        # self.page.wait_for_load_state(state="load")
        logger.info(f"Navigated to {asset_type} page with sid: {asset_id}")


    def verify_asset_charts(self, asset_type: str) -> None:
        """
        :param asset_type: Asset type to check charts for  (stocks, etfs, indices, mutualfunds)
        :return: None
        """
        logger.info("Verifying asset charts")
        durations: list[str] = []
        if asset_type == ASSET_TYPE.MUTUALFUND:
            durations = ["1mo", "6mo", "1y", "3y", "5y", "max", "sip"]
        else:
            durations = ["1d", "1w", "1mo", "1y", "5y", "max", "sip"]

        for duration in durations:
            logger.info(f"Verifying {asset_type} charts for {duration} duration")
            self.click_on_duration_switcher(duration)
            self.page.wait_for_timeout(1500)
            self.verify_chart_metrices(asset_type, duration)
            self.verify_is_chart_displayed()
            logger.info(f"Verified {asset_type} charts for {duration} duration")
        logger.info("Verified asset charts")


    def click_on_duration_switcher(self, duration: str) -> None:
        logger.info(f"clicking on the {duration} chart duration switcher")
        chart_selector: Locator = self.page.locator("xpath=//div[contains(@class,'desktop--only')]/div/input[@value='" + duration + "']")
        chart_selector.click()


    def verify_chart_metrices(self, asset_type: str, duration: str):
        logger.info(f"Verifying chart metrices for {asset_type} for duration: {duration}")
        if duration == "sip":
            self.Verify_investment_value_and_return(asset_type, duration)
        elif asset_type == ASSET_TYPE.STOCK:
            self.Verify_high_low_and_returns(asset_type,duration)
        elif asset_type == ASSET_TYPE.MUTUALFUND and duration in [ "1mo", "6mo", "1y"]:
            self.Verify_high_low_and_returns(asset_type,duration)
        elif asset_type == ASSET_TYPE.MUTUALFUND and duration in ["3y", "5y", "max"]:
            self.Verify_high_low_and_cagr(asset_type, duration)
        else:
            logger.warning(f"Unexpected asset_type: {asset_type} or duration: {duration}")
        logger.info(f"Verified chart metrics for {asset_type} for duration: {duration}")


    def Verify_high_low_and_returns(self, asset_type: str, duration: str):
        assert self.page.locator(self.CHART_HIGH_PRICE_LABEL).nth(0).is_visible(), \
            f"High price label is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHART_LOW_PRICE_LABEL).nth(0).is_visible(), \
            f"Low price label is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHARTS_RETURNS_LABEL).nth(0).is_visible(), \
            f"Returns label is not visible for {asset_type} for duration: {duration}"

        assert self.page.locator(self.CHART_HIGH_PRICE_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHART_LOW_PRICE_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHARTS_RETURNS_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"

    def Verify_high_low_and_cagr(self, asset_type: str, duration: str):
        assert self.page.locator(self.CHART_HIGH_PRICE_LABEL).nth(0).is_visible(), \
            f"High price label is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHART_LOW_PRICE_LABEL).nth(0).is_visible(), \
            f"Low price label is not visible for {asset_type} for duration: {duration}"
        if self.page.locator(self.MAX_CAGR_LABEL).count() != 0:
            assert self.page.locator(self.MAX_CAGR_LABEL).nth(0).is_visible(), \
                f"Max cagr label is not visible for {asset_type} for duration: {duration}"
            assert self.page.locator(self.MAX_CAGR_VALUE).nth(0).inner_text() != "", \
                f"Max cagr value is not visible for {asset_type} for duration: {duration}"
        else:
            assert self.page.locator(self.CAGR_LABEL).nth(0).is_visible(), \
                f"Returns label is not visible for {asset_type} for duration: {duration}"
            assert self.page.locator(self.CAGR_VALUE).nth(0).inner_text() != "", \
                f"Returns Value is not visible for {asset_type} for duration: {duration}"

        assert self.page.locator(self.CHART_HIGH_PRICE_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHART_LOW_PRICE_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"


    def Verify_investment_value_and_return(self, asset_type: str, duration: str):
        assert self.page.locator(self.INVESTMENT_LABEL).nth(0).is_visible(), \
            f"Investment label is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.VALUE_LABEL).nth(0).is_visible(), \
            f"Value label is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHARTS_RETURNS_LABEL).nth(0).is_visible(), \
            f"Returns label is not visible for {asset_type} for duration: {duration}"

        assert self.page.locator(self.INVESTMENT_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.VALUE_DATA).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"
        assert self.page.locator(self.CHARTS_RETURNS_VALUE).nth(0).inner_text() != "", \
            f"Returns Value is not visible for {asset_type} for duration: {duration}"

    def verify_is_chart_displayed(self):
        logger.info("Verifying chart is displayed")
        assert self.page.locator(self.CHART_LINE).nth(0).is_visible(), \
            "Chart line is not visible"
