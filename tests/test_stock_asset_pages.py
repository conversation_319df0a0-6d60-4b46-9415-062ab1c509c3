import pytest

from API.screener_helper import ScreenerHelper
from pages.asset_pages.asset_page import AssetPage
from pages.asset_pages.stock_page import StockPage
from utils.config import STOCKS_SID_LIST, ASSET_TYPE, USER_TYPE
from utils.helpers import CommonHelper
from utils.logger import logger


@pytest.mark.stocks
@pytest.mark.parametrize("user_type",[USER_TYPE.LOGGED_OUT])
def test_stock_page(page, user_type):
    common_helper_obj = CommonHelper(page)
    asset_page_obj = AssetPage(page)
    stock_page_obj = StockPage(page)
    for sid in STOCKS_SID_LIST:
        logger.info(f"Testing stock page for {sid}")
        asset_page_obj.navigate_to_asset_page(ASSET_TYPE.STOCK, sid)
        page.wait_for_load_state(state="networkidle")
        common_helper_obj.close_web_ad_unit(user_type)
        asset_page_obj.verify_asset_charts(ASSET_TYPE.STOCK)
        stock_page_obj.verify_key_metrics_section(user_type)
        stock_page_obj.verify_forecast_and_ratings_section(user_type)
        stock_page_obj.check_sector_collection_page_redirection()
        stock_page_obj.check_subsector_collection_page_redirection()\

@pytest.fixture
def sector_sub_sector_dicts():
    screener_helper = ScreenerHelper()
    sector_dict, sub_sector_dict = screener_helper.get_sector_sub_sector_list()
    print(f"sub sector dict: {sub_sector_dict.values()}")
    print(f"sub sector dict: {sub_sector_dict}")
    return sector_dict, sub_sector_dict

@pytest.mark.tags
@pytest.mark.parametrize("sub_sector_dict",sector_sub_sector_dicts[1].values())
def test_stock_page_sub_sector_tags(page, sub_sector_dict):
    screener_helper = ScreenerHelper()
    # Getting sector and sub sector list from screener API

    for sub_sector in sub_sector_dict:
        logger.info(f"Fetching results for sub sector: {sub_sector_dict[sub_sector]}")
        stock_id = screener_helper.query_screener_for_sub_sector(sub_sector=sub_sector)
        if stock_id is None:
            logger.warning(f"No stock found for the sub sector: {sub_sector_dict[sub_sector]}")
            continue
        logger.info(f"Stock for the sub sector {sub_sector_dict[sub_sector]}: {stock_id}")
        # visit each page and verify the tags are present
        asset_page_obj = AssetPage(page)
        stock_page_obj = StockPage(page)
        asset_page_obj.navigate_to_asset_page(ASSET_TYPE.STOCK, stock_id)
        common_helper_obj = CommonHelper(page)
        common_helper_obj.close_web_ad_unit()
        stock_page_obj.move_to_stock_page_section("overview")
        # print("Sector name: "+stock_page_obj.get_sector_name())
        # print("Sub sector name: "+stock_page_obj.get_subsector_name())
        logger.debug("Validating sub sector tag")

        assert stock_page_obj.get_subsector_name() == sub_sector_dict[sub_sector], f"Sub sector name is not matching for {stock_id}"
        try:
            stock_page_obj.check_subsector_collection_page_redirection()
            logger.info(f"Verified {len(sub_sector_dict)} sub sector tags")
        except Exception as e:
            logger.error(f"Failed to verify {len(sub_sector_dict)} sub sector tag for {stock_id}")
            continue
